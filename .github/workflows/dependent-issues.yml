name: Dependent Issues

on:
  schedule:
    - cron: '*/15 * * * *' # run every 15 minutes
  workflow_dispatch:

jobs:
  cancel:
    name: 'Cancel Previous Runs (dependent issues)'
    runs-on: ubuntu-latest
    if: github.repository == 'leanprover-community/mathlib4'
    # timeout-minutes: 3
    steps:
      - uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # 0.12.1
        with:
          all_but_latest: true
          access_token: ${{ github.token }}

  check:
    runs-on: ubuntu-latest
    if: github.repository == 'leanprover-community/mathlib4'
    steps:
      - uses: z0al/dependent-issues@75d554cd9494b6e1766bc9d08a81c26444ad5c5a
        env:
          # (Required) The token to use to make API calls to GitHub.
          GITHUB_TOKEN: ${{ secrets.DEPENDENT_ISSUES_TOKEN }}
        with:
          # (Optional) The label to use to mark dependent issues
          label: blocked-by-other-PR

          # (Optional) Enable checking for dependencies in issues. Enable by
          # setting the value to "on". Default "off"
          check_issues: off

          # (Optional) A comma-separated list of keywords. Default
          # "depends on, blocked by"
          keywords: "- \\[ \\] depends on:,- \\[x\\] depends on:"
