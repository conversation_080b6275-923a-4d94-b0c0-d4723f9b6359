name: Zulip emoji merge update

on:
  push:
    branches:
      - master
      - bump/*

jobs:
  zulip-emoji-merged:
    runs-on: ubuntu-latest
    if: github.repository == 'leanprover-community/mathlib4'

    steps:
    - name: Checkout mathlib repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        fetch-depth: 0 # download the full repository

    - name: Set up Python
      uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
      with:
        python-version: '3.x'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install zulip

    - name: Update zulip emoji reactions
      env:
        ZULIP_API_KEY: ${{ secrets.ZULIP_API_KEY }}
        ZULIP_EMAIL: <EMAIL>
        ZULIP_SITE: https://leanprover.zulipchat.com
      run: |
          # scan the commits of the past 10 minutes, assuming that the timezone of the current machine
          # is the same that performed the commit
          git log --since="10 minutes ago" --pretty=oneline

          printf $'Scanning commits:\n%s\n\nContinuing\n' "$(git log --since="10 minutes ago" --pretty=oneline)"
          while read -r pr_number; do
            printf $'Running the python script with pr "%s"\n' "${pr_number}"
            python scripts/zulip_emoji_reactions.py "$ZULIP_API_KEY" "$ZULIP_EMAIL" "$ZULIP_SITE" "[Merged by Bors]" "none" "${pr_number}"
          done < <(git log --oneline -n 10 | awk -F# '{ gsub(/)$/, ""); print $NF}')
