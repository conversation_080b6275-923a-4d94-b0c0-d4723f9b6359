name: docker
on:
  schedule:
    - cron: '0 0 * * *'   # Runs at 00:00 UTC every day
  workflow_dispatch:
env:
  # Possibly change this to `hub.docker.com` in the future
  REGISTRY: ghcr.io
  REPO_NAME: ${{ github.repository }}
jobs:
  build-and-push-docker-images:
    if: github.repository == 'leanprover-community/mathlib4'
    strategy:
      fail-fast: false
      matrix:
        image:
          - gitpod
          - gitpod-blueprint
          - lean
    name: Build and push docker image '${{ matrix.image }}'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    steps:
      # documentation at
      # https://docs.github.com/en/actions/use-cases-and-examples/publishing-packages/publishing-docker-images
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Log in to the container registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ${{ env.REGISTRY }}
          # might need set as `secrets.DOCKER_USERNAME` and `secrets.DOCKER_PASSWORD`
          # if using different registry than ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata (tags, labels) for docker
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: ${{ env.REGISTRY }}/${{ env.REPO_NAME }}/${{ matrix.image }}
          tags: |
            type=schedule,pattern={{date 'YYYY-MM-DD'}}
            type=sha
            type=ref,event=branch
            type=ref,event=tag
            type=raw,value=latest
          labels: |
            org.opencontainers.image.licenses=Apache-2.0
      - name: Build and push docker image
        id: push
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: .
          file: .docker/${{ matrix.image }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@db473fddc028af60658334401dc6fa3ffd8669fd # v2.3.0
        with:
          subject-name: ${{ env.REGISTRY }}/${{ env.REPO_NAME }}/${{ matrix.image }}
          subject-digest: ${{ steps.push.outputs.digest }}
          push-to-registry: true
