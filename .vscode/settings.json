{"editor.insertSpaces": true, "editor.tabSize": 2, "editor.rulers": [100], "files.encoding": "utf8", "files.eol": "\n", "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "githubPullRequests.ignoredPullRequestBranches": ["master"], "git.ignoreLimitWarning": true, "git.branchProtection": ["master", "stable", "staging"], "files.readonlyInclude": {"**/.elan/**": true, "**/.lake/**": true}}